import { LRUCache } from "lru-cache"

import { ProjectDependencyAnalyzer } from "./ProjectDependencyAnalyzer"

/**
 * 跨文件定义服务
 * 负责生成跨文件感知内容，类似list_code_definition_names但包含类成员属性
 */
export class CrossFileDefinitionsService {
	// 跨文件感知内容缓存，最多保留20条记录
	private crossFileDefinitionsCache = new LRUCache<string, string>({
		max: 20,
	})

	private projectDependencyAnalyzer: ProjectDependencyAnalyzer

	constructor() {
		this.projectDependencyAnalyzer = new ProjectDependencyAnalyzer()
	}

	/**
	 * 生成跨文件感知内容，类似list_code_definition_names但包含类成员属性
	 * @param filePath 待测文件路径
	 * @returns 跨文件感知内容字符串
	 */
	async generateCrossFileDefinitions(filePath: string): Promise<string> {
		try {
			// 检查缓存
			const cached = this.crossFileDefinitionsCache.get(filePath)
			if (cached) {
				console.debug(`Using cached cross file definitions for ${filePath}`)
				return cached
			}

			// 使用新的项目依赖分析器
			const result = await this.projectDependencyAnalyzer.analyzeCrossFileDefinitions(filePath)

			// 缓存结果
			if (result) {
				this.crossFileDefinitionsCache.set(filePath, result)
			}

			return result
		} catch (error) {
			console.warn(`Failed to generate cross file definitions for ${filePath}:`, error)
			return ""
		}
	}

}
