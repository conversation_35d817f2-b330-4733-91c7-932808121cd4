import * as vscode from "vscode"
import * as path from "path"
import { LRUCache } from "lru-cache"

import { getWorkspacePath, getReadablePath } from "../utils/path"
import { parseSourceCodeDefinitionsForFile } from "./tree-sitter"
import { getParserForFile, getQueryForFile, getFullLanguageName } from "../cross-file/util/treeSitter"
import { readFile } from "../cross-file/util/ide"

/**
 * 导入类型枚举
 */
type ImportType = 'standard' | 'third-party' | 'project'

/**
 * 导入信息接口
 */
interface ImportInfo {
	importName: string
	importType: ImportType
	sourceLocation: {
		line: number
		column: number
	}
	importPath?: string // JavaScript/TypeScript特有
}

/**
 * 项目依赖信息接口
 */
interface ProjectDependency {
	importName: string
	filePath: string
	importType: ImportType
	sourceLocation: {
		line: number
		column: number
	}
}

/**
 * 项目依赖分析结果接口
 */
interface ProjectDependencyInfo {
	filePath: string
	allImports: ImportInfo[]
	projectDependencies: ProjectDependency[]
	analyzedAt: Date
}

/**
 * 项目依赖分析器
 * 独立实现跨文件依赖分析，不依赖ImportDefinitionsService
 */
export class ProjectDependencyAnalyzer {
	// 依赖分析结果缓存，最多保留50条记录
	private dependencyCache = new LRUCache<string, ProjectDependencyInfo>({
		max: 50,
	})

	// 代码定义缓存，最多保留100条记录
	private definitionCache = new LRUCache<string, string>({
		max: 100,
	})

	/**
	 * 分析文件的项目内部依赖并生成跨文件定义信息
	 * @param filePath 待分析文件路径
	 * @returns 跨文件定义信息字符串
	 */
	async analyzeCrossFileDefinitions(filePath: string): Promise<string> {
		try {
			// 检查缓存
			const cacheKey = `definitions_${filePath}`
			const cached = this.definitionCache.get(cacheKey)
			if (cached) {
				console.debug(`Using cached cross file definitions for ${filePath}`)
				return cached
			}

			// 分析项目依赖
			const dependencyInfo = await this.analyzeProjectDependencies(filePath)
			if (!dependencyInfo || dependencyInfo.projectDependencies.length === 0) {
				console.debug(`No project dependencies found for ${filePath}`)
				return ""
			}

			let result = ""
			let successfulCount = 0
			const workspaceRoot = getWorkspacePath()

			// 处理每个项目依赖文件，最多处理20个
			const maxFiles = Math.min(dependencyInfo.projectDependencies.length, 20)
			for (let i = 0; i < maxFiles; i++) {
				const depFile = dependencyInfo.projectDependencies[i]
				
				try {
					// 获取文件的代码定义
					const definitions = await this.getFileDefinitions(depFile.filePath)
					if (definitions) {
						const relativePath = getReadablePath(workspaceRoot, depFile.filePath)
						result += `[list_cross_code_definition_names for '${relativePath}'] Result:\n${definitions}\n\n`
						successfulCount++
						console.debug(`Successfully generated definitions for ${depFile.importName} -> ${relativePath}`)
					}
				} catch (error) {
					const relativePath = getReadablePath(workspaceRoot, depFile.filePath)
					console.warn(`Failed to generate definitions for ${depFile.importName} -> ${relativePath}:`, error)
				}
			}

			console.debug(`Generated cross file definitions: ${successfulCount}/${maxFiles} successful`)

			// 缓存结果
			if (result) {
				this.definitionCache.set(cacheKey, result)
			}

			return result
		} catch (error) {
			console.warn(`Failed to analyze cross file definitions for ${filePath}:`, error)
			return ""
		}
	}

	/**
	 * 分析文件的项目依赖
	 * @param filePath 文件路径
	 * @returns 项目依赖信息
	 */
	async analyzeProjectDependencies(filePath: string): Promise<ProjectDependencyInfo | null> {
		try {
			// 检查缓存
			const cached = this.dependencyCache.get(filePath)
			if (cached) {
				return cached
			}

			// 解析文件导入
			const imports = await this.parseFileImports(filePath)
			if (!imports || imports.length === 0) {
				return null
			}

			// 筛选项目内部依赖
			const projectDependencies = await this.filterProjectDependencies(filePath, imports)

			const result: ProjectDependencyInfo = {
				filePath,
				allImports: imports,
				projectDependencies,
				analyzedAt: new Date(),
			}

			// 缓存结果
			this.dependencyCache.set(filePath, result)

			return result
		} catch (error) {
			console.warn(`Failed to analyze project dependencies for ${filePath}:`, error)
			return null
		}
	}

	/**
	 * 解析文件的导入语句
	 * @param filePath 文件路径
	 * @returns 导入信息数组
	 */
	private async parseFileImports(filePath: string): Promise<ImportInfo[]> {
		try {
			const language = getFullLanguageName(filePath)
			if (!language) {
				console.debug(`Unsupported language for file: ${filePath}`)
				return []
			}

			// 特殊处理JavaScript/TypeScript
			if (language === "javascript") {
				return await this.parseJavaScriptImports(filePath)
			}

			// 使用Tree-sitter解析其他语言
			const parser = await getParserForFile(filePath)
			if (!parser) {
				console.debug(`No parser available for file: ${filePath}`)
				return []
			}

			const fileContents = await readFile(vscode.Uri.file(filePath))
			if (!fileContents.trim()) {
				return []
			}

			const ast = parser.parse(fileContents, undefined, {
				includedRanges: [
					{
						startIndex: 0,
						endIndex: 10_000,
						startPosition: { row: 0, column: 0 },
						endPosition: { row: 100, column: 0 },
					},
				],
			})

			if (!ast) {
				return []
			}

			const query = await getQueryForFile(filePath)
			if (!query) {
				return []
			}

			const matches = query.matches(ast.rootNode)
			const imports: ImportInfo[] = []
			const processedImports = new Set<string>()

			for (const match of matches) {
				const importText = match.captures[0].node.text

				// 跳过已处理的导入和过短的标识符
				if (processedImports.has(importText) || importText.length < 2) {
					continue
				}
				processedImports.add(importText)

				// 跳过内置类型
				if (this.isBuiltinType(importText, language)) {
					continue
				}

				imports.push({
					importName: importText,
					importType: this.detectImportType(importText, language),
					sourceLocation: {
						line: match.captures[0].node.startPosition.row,
						column: match.captures[0].node.startPosition.column,
					},
				})
			}

			return imports
		} catch (error) {
			console.warn(`Failed to parse imports for ${filePath}:`, error)
			return []
		}
	}

	/**
	 * 解析JavaScript/TypeScript导入（简化版本）
	 * @param filePath 文件路径
	 * @returns 导入信息数组
	 */
	private async parseJavaScriptImports(filePath: string): Promise<ImportInfo[]> {
		try {
			const fileContents = await readFile(vscode.Uri.file(filePath))
			const imports: ImportInfo[] = []
			const lines = fileContents.split('\n')

			// 匹配import语句
			for (let i = 0; i < lines.length; i++) {
				const line = lines[i]
				
				// ES6 import
				const importMatches = [...line.matchAll(/import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"]([^'"]+)['"]/g)]
				for (const match of importMatches) {
					const importPath = match[1]
					if (importPath) {
						imports.push({
							importName: path.basename(importPath, path.extname(importPath)),
							importType: this.detectImportType(importPath, "javascript"),
							sourceLocation: { line: i, column: match.index || 0 },
							importPath,
						})
					}
				}

				// CommonJS require
				const requireMatches = [...line.matchAll(/(?:const|let|var)\s+(?:\{[^}]*\}|\w+)\s*=\s*require\s*\(\s*['"]([^'"]+)['"]\s*\)/g)]
				for (const match of requireMatches) {
					const importPath = match[1]
					if (importPath) {
						imports.push({
							importName: path.basename(importPath, path.extname(importPath)),
							importType: this.detectImportType(importPath, "javascript"),
							sourceLocation: { line: i, column: match.index || 0 },
							importPath,
						})
					}
				}
			}

			return imports
		} catch (error) {
			console.warn(`Failed to parse JavaScript imports for ${filePath}:`, error)
			return []
		}
	}

	/**
	 * 筛选项目内部依赖
	 * @param filePath 当前文件路径
	 * @param imports 所有导入信息
	 * @returns 项目依赖数组
	 */
	private async filterProjectDependencies(filePath: string, imports: ImportInfo[]): Promise<ProjectDependency[]> {
		const projectDependencies: ProjectDependency[] = []
		const workspaceRoot = getWorkspacePath()

		for (const importInfo of imports) {
			// 跳过标准库和第三方库
			if (importInfo.importType !== 'project') {
				continue
			}

			// 解析导入路径到实际文件路径
			const resolvedPaths = await this.resolveImportToFilePaths(
				importInfo.importName,
				filePath,
				workspaceRoot,
				importInfo.importPath
			)

			// 添加找到的项目文件
			for (const resolvedPath of resolvedPaths) {
				if (this.isProjectFile(resolvedPath, workspaceRoot)) {
					projectDependencies.push({
						importName: importInfo.importName,
						filePath: resolvedPath,
						importType: importInfo.importType,
						sourceLocation: importInfo.sourceLocation,
					})
				}
			}
		}

		return projectDependencies
	}

	/**
	 * 解析导入名称到文件路径
	 * @param importName 导入名称
	 * @param currentFilePath 当前文件路径
	 * @param workspaceRoot 工作空间根目录
	 * @param importPath 导入路径（可选）
	 * @returns 文件路径数组
	 */
	private async resolveImportToFilePaths(
		importName: string,
		currentFilePath: string,
		workspaceRoot: string,
		importPath?: string
	): Promise<string[]> {
		const resolvedPaths: string[] = []

		try {
			// JavaScript/TypeScript相对路径解析
			if (importPath) {
				if (importPath.startsWith('.')) {
					const currentDir = path.dirname(currentFilePath)
					const resolvedPath = path.resolve(currentDir, importPath)

					// 尝试不同的文件扩展名
					const extensions = ['.ts', '.js', '.tsx', '.jsx', '.java', '.py']
					for (const ext of extensions) {
						const fullPath = resolvedPath + ext
						try {
							const uri = vscode.Uri.file(fullPath)
							await vscode.workspace.fs.stat(uri)
							resolvedPaths.push(fullPath)
						} catch {
							// 文件不存在，继续尝试
						}
					}
				}
				return resolvedPaths
			}

			// 通用文件名搜索
			const foundFiles = await this.findFilesByName(importName, workspaceRoot)
			resolvedPaths.push(...foundFiles)

			// Python模块解析
			if (currentFilePath.endsWith('.py')) {
				const pythonFiles = await this.resolvePythonModule(importName, workspaceRoot)
				resolvedPaths.push(...pythonFiles)
			}

		} catch (error) {
			console.warn(`Failed to resolve import ${importName}:`, error)
		}

		return resolvedPaths
	}

	/**
	 * 解析Python模块
	 * @param moduleName 模块名
	 * @param workspaceRoot 工作空间根目录
	 * @returns 文件路径数组
	 */
	private async resolvePythonModule(moduleName: string, workspaceRoot: string): Promise<string[]> {
		try {
			const searchPattern = `**/${moduleName}.py`
			const files = await vscode.workspace.findFiles(
				new vscode.RelativePattern(workspaceRoot, searchPattern),
				"**/venv/**",
				5
			)

			return files.map(file => file.fsPath)
		} catch (error) {
			console.warn(`Failed to resolve Python module ${moduleName}:`, error)
			return []
		}
	}

	/**
	 * 通用文件名搜索
	 * @param fileName 文件名
	 * @param workspaceRoot 工作空间根目录
	 * @returns 文件路径数组
	 */
	private async findFilesByName(fileName: string, workspaceRoot: string): Promise<string[]> {
		try {
			const searchPattern = `**/${fileName}.*`
			const files = await vscode.workspace.findFiles(
				new vscode.RelativePattern(workspaceRoot, searchPattern),
				"**/node_modules/**",
				5
			)

			return files.map(file => file.fsPath)
		} catch (error) {
			console.warn(`Failed to find files by name ${fileName}:`, error)
			return []
		}
	}

	/**
	 * 获取文件的代码定义
	 * @param filePath 文件路径
	 * @returns 代码定义字符串
	 */
	private async getFileDefinitions(filePath: string): Promise<string | null> {
		try {
			// 检查缓存
			const cached = this.definitionCache.get(filePath)
			if (cached) {
				return cached
			}

			// 使用parseSourceCodeDefinitionsForFile获取定义
			const definitions = await parseSourceCodeDefinitionsForFile(filePath)

			if (definitions) {
				// 缓存结果
				this.definitionCache.set(filePath, definitions)
			}

			return definitions || null
		} catch (error) {
			console.warn(`Failed to get file definitions for ${filePath}:`, error)
			return null
		}
	}

	/**
	 * 检测导入类型
	 * @param importName 导入名称
	 * @param language 编程语言
	 * @returns 导入类型
	 */
	private detectImportType(importName: string, language: string): ImportType {
		// 标准库检测
		if (this.isStandardLibrary(importName, language)) {
			return 'standard'
		}

		// 第三方库检测
		if (this.isThirdPartyLibrary(importName, language)) {
			return 'third-party'
		}

		// 默认为项目内部
		return 'project'
	}

	/**
	 * 检查是否为标准库
	 * @param importName 导入名称
	 * @param language 编程语言
	 * @returns 是否为标准库
	 */
	private isStandardLibrary(importName: string, language: string): boolean {
		const standardLibraries: Record<string, string[]> = {
			java: [
				'java.', 'javax.', 'org.w3c.', 'org.xml.', 'org.ietf.',
				'String', 'Integer', 'Long', 'Double', 'Float', 'Boolean',
				'List', 'Map', 'Set', 'Collection', 'ArrayList', 'HashMap',
			],
			javascript: [
				'fs', 'path', 'http', 'https', 'url', 'crypto', 'os',
				'util', 'events', 'stream', 'buffer', 'child_process',
			],
			python: [
				'os', 'sys', 'json', 'time', 'datetime', 'math', 'random',
				'collections', 'itertools', 'functools', 're', 'urllib',
			],
		}

		const libs = standardLibraries[language] || []
		return libs.some(lib => importName.startsWith(lib) || importName === lib)
	}

	/**
	 * 检查是否为第三方库
	 * @param importName 导入名称
	 * @param language 编程语言
	 * @returns 是否为第三方库
	 */
	private isThirdPartyLibrary(importName: string, language: string): boolean {
		// 简单的第三方库检测逻辑
		if (language === 'javascript') {
			// Node.js第三方包通常不以.开头且不是相对路径
			return !importName.startsWith('.') && !importName.startsWith('/') &&
				   !this.isStandardLibrary(importName, language)
		}

		if (language === 'java') {
			// 常见的第三方库前缀
			const thirdPartyPrefixes = [
				'org.springframework.', 'org.apache.', 'com.google.',
				'org.junit.', 'org.mockito.', 'lombok.',
			]
			return thirdPartyPrefixes.some(prefix => importName.startsWith(prefix))
		}

		return false
	}

	/**
	 * 检查是否为内置类型
	 * @param text 文本
	 * @param language 编程语言
	 * @returns 是否为内置类型
	 */
	private isBuiltinType(text: string, language: string): boolean {
		if (language === "java") {
			const javaBuiltinTypes = new Set([
				// 基本类型
				"byte", "short", "int", "long", "float", "double", "boolean", "char",
				// 包装类型
				"Byte", "Short", "Integer", "Long", "Float", "Double", "Boolean", "Character",
				// 常用类型
				"String", "Object", "Class", "Void",
				// 关键字
				"public", "private", "protected", "static", "final", "abstract",
				"class", "interface", "enum", "extends", "implements",
				"import", "package", "return", "void", "if", "else", "for", "while",
				"try", "catch", "finally", "throw", "throws", "new", "this", "super",
				"null", "true", "false", "instanceof", "assert",
			])

			return javaBuiltinTypes.has(text) || text.length <= 1 || /^\d+$/.test(text)
		}

		// 其他语言的内置类型检测可以在这里添加
		return text.length <= 1 || /^\d+$/.test(text)
	}

	/**
	 * 检查文件是否属于项目
	 * @param filePath 文件路径
	 * @param workspaceRoot 工作空间根目录
	 * @returns 是否为项目文件
	 */
	private isProjectFile(filePath: string, workspaceRoot: string): boolean {
		// 检查是否在工作空间目录下
		if (!filePath.startsWith(workspaceRoot)) {
			return false
		}

		// 排除常见的非项目文件目录
		const excludePatterns = [
			'/node_modules/',
			'/target/',
			'/build/',
			'/dist/',
			'/.git/',
			'/venv/',
			'/__pycache__/',
		]

		return !excludePatterns.some(pattern => filePath.includes(pattern))
	}
}
