import { describe, it, expect, beforeEach, vi } from "vitest"
import { ProjectDependencyAnalyzer } from "../ProjectDependencyAnalyzer"

// Mock vscode module
vi.mock("vscode", () => ({
	Uri: {
		file: vi.fn((path: string) => ({ fsPath: path })),
	},
	workspace: {
		findFiles: vi.fn().mockResolvedValue([]),
		fs: {
			stat: vi.fn().mockRejectedValue(new Error("File not found")),
		},
	},
	RelativePattern: vi.fn(),
}))

// Mock other dependencies
vi.mock("../tree-sitter", () => ({
	parseSourceCodeDefinitionsForFile: vi.fn().mockResolvedValue("mock definitions"),
}))

vi.mock("../../utils/path", () => ({
	getWorkspacePath: vi.fn().mockReturnValue("/mock/workspace"),
	getReadablePath: vi.fn().mockReturnValue("mock/relative/path"),
}))

vi.mock("../../cross-file/util/treeSitter", () => ({
	getFullLanguageName: vi.fn().mockReturnValue("java"),
	getParserForFile: vi.fn().mockResolvedValue(null),
	getQueryForFile: vi.fn().mockResolvedValue(null),
}))

vi.mock("../../cross-file/util/ide", () => ({
	readFile: vi.fn().mockResolvedValue("mock file content"),
}))

describe("ProjectDependencyAnalyzer", () => {
	let analyzer: ProjectDependencyAnalyzer

	beforeEach(() => {
		analyzer = new ProjectDependencyAnalyzer()
		vi.clearAllMocks()
	})

	describe("analyzeCrossFileDefinitions", () => {
		it("should return empty string for files without dependencies", async () => {
			const result = await analyzer.analyzeCrossFileDefinitions("/mock/file.java")
			expect(result).toBe("")
		})

		it("should use caching correctly", async () => {
			// First call
			const result1 = await analyzer.analyzeCrossFileDefinitions("/mock/file.java")
			
			// Second call should use cache
			const result2 = await analyzer.analyzeCrossFileDefinitions("/mock/file.java")
			
			expect(result1).toBe(result2)
		})
	})

	describe("analyzeProjectDependencies", () => {
		it("should return null for files without imports", async () => {
			const result = await analyzer.analyzeProjectDependencies("/mock/file.java")
			expect(result).toBeNull()
		})
	})

	describe("service initialization", () => {
		it("should create analyzer instance", () => {
			expect(analyzer).toBeDefined()
		})

		it("should have analyzeCrossFileDefinitions method", () => {
			expect(typeof analyzer.analyzeCrossFileDefinitions).toBe("function")
		})

		it("should have analyzeProjectDependencies method", () => {
			expect(typeof analyzer.analyzeProjectDependencies).toBe("function")
		})
	})
})
