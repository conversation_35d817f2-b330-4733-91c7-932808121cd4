# ProjectDependencyAnalyzer 使用示例

## 概述

新的 `ProjectDependencyAnalyzer` 是一个独立的跨文件依赖分析服务，不依赖 `ImportDefinitionsService`，实现了从输入文件到项目内部依赖的完整分析流程。

## 主要特性

1. **独立实现**: 不依赖 ImportDefinitionsService，完全独立的依赖分析
2. **多语言支持**: 支持 Java、JavaScript/TypeScript、Python 等多种编程语言
3. **智能过滤**: 自动过滤标准库和第三方依赖，只关注项目内部依赖
4. **高性能缓存**: 使用 LRU 缓存机制，提高分析性能
5. **代码定义提取**: 集成 `parseSourceCodeDefinitionsForFile` 工具提取代码定义

## 使用方式

### 1. 直接使用 ProjectDependencyAnalyzer

```typescript
import { ProjectDependencyAnalyzer } from './services/ProjectDependencyAnalyzer'

const analyzer = new ProjectDependencyAnalyzer()

// 分析跨文件定义
const crossFileDefinitions = await analyzer.analyzeCrossFileDefinitions('/path/to/file.java')
console.log(crossFileDefinitions)

// 分析项目依赖
const dependencies = await analyzer.analyzeProjectDependencies('/path/to/file.java')
console.log(dependencies)
```

### 2. 通过 CrossFileDefinitionsService

```typescript
import { CrossFileDefinitionsService } from './services/CrossFileDefinitionsService'

const service = new CrossFileDefinitionsService()
const result = await service.generateCrossFileDefinitions('/path/to/file.java')
console.log(result)
```

### 3. 通过工具调用

```xml
<list_cross_code_definition_names>
<path>src/main/java/com/example/controller/UserController.java</path>
</list_cross_code_definition_names>
```

## 工作流程

1. **输入文件**: 接收待分析的文件路径
2. **解析导入**: 使用 Tree-sitter 或正则表达式解析文件的导入语句
3. **类型检测**: 识别导入类型（标准库、第三方库、项目内部）
4. **路径解析**: 将导入名称解析为实际的文件路径
5. **依赖筛选**: 过滤出项目内部依赖文件
6. **定义提取**: 使用 `parseSourceCodeDefinitionsForFile` 提取代码定义
7. **结果格式化**: 格式化输出跨文件感知内容

## 输出格式

```
[list_cross_code_definition_names for 'src/service/UserService.java'] Result:
# UserService.java
1--10 | public class UserService {
11--15 |     private UserRepository userRepository;
16--25 |     public User findById(Long id) { ... }
26--35 |     public List<User> findAll() { ... }
}

[list_cross_code_definition_names for 'src/model/User.java'] Result:
# User.java
1--5 | public class User {
6--10 |     private Long id;
11--15 |     private String name;
16--25 |     // getters and setters
}
```

## 性能优化

- **依赖分析缓存**: 最多缓存 50 个文件的依赖分析结果
- **定义内容缓存**: 最多缓存 100 个文件的代码定义
- **限制分析数量**: 每个文件最多分析 20 个依赖文件，避免过度分析
- **异步处理**: 所有文件操作都是异步的，不阻塞主流程

## 支持的语言

- **Java**: 支持 import 语句解析和类定义提取
- **JavaScript/TypeScript**: 支持 ES6 import 和 CommonJS require
- **Python**: 支持 import 和 from...import 语句
- **其他语言**: 通过 Tree-sitter 查询支持更多语言

## 错误处理

- 文件不存在时返回空字符串
- 解析错误时记录警告并继续处理其他文件
- 网络或文件系统错误时优雅降级
- 所有错误都有详细的日志记录

## 与原有系统的兼容性

- 完全向后兼容，不影响现有功能
- 可以与原有的 ImportDefinitionsService 并存
- 缓存机制独立，不会冲突
- API 接口保持一致
